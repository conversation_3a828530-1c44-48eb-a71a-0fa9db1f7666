// Helper function to get cookie by name
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            // Does this cookie string begin with the name we want?
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

let currentSort = { field: null, direction: 'asc' };

function sortBy(field) {
    if (currentSort.field === field) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.field = field;
        currentSort.direction = 'asc';
    }
    window.externalUpdateResults();
    updateSortArrows();
}

// For backward compatibility
function updateResults() {
    window.externalUpdateResults();
}

// Export the function to the global scope so it can be called from inline scripts
window.externalUpdateResults = async function() {

    const formData = new FormData(document.querySelector('form'));
    const data = Object.fromEntries(formData.entries());
    data.origin = formData.getAll('origin');
    data.flavors = formData.getAll('flavors');

    // Get state from buttons
    const arabicaBtn = document.getElementById('arabicaBtn');
    const robustaBtn = document.getElementById('robustaBtn');
    const singOBtn = document.getElementById('singOBtn'); // Add single origin button reference
    const mixBtn = document.getElementById('mixBtn');
    const specialityBtn = document.getElementById('specialityBtn');
    const decafBtn = document.getElementById('decafBtn');

    data.arabica = arabicaBtn && arabicaBtn.classList.contains('active') ? 1 : 0;
    data.robusta = robustaBtn && robustaBtn.classList.contains('active') ? 1 : 0;
    data.singleorigin = singOBtn && singOBtn.classList.contains('active') ? 1 : 0; // Add single origin flag
    data.mix = mixBtn && mixBtn.classList.contains('active') ? 1 : 0;
    data.speciality = specialityBtn && specialityBtn.classList.contains('active') ? 1 : 0;
    data.decaf = decafBtn && decafBtn.classList.contains('active') ? 1 : 0;

    // Get elevation value
    const elevationSelect = document.getElementById('elevation');
    data.elevation = elevationSelect ? elevationSelect.value : '';

    // Include roaster search criteria from the global selectedRoasters array
    data.roasters = typeof selectedRoasters !== 'undefined' ? selectedRoasters.map(r => r.id) : [];

    // Collect brew methods from checkboxes
    const brewFilters = document.querySelectorAll('.brew-filter:checked');
    data.brew_methods = Array.from(brewFilters).map(cb => cb.value);

    data.flavor_mode = window.flavorMode;



    // If no filters are applied, clear table and skip fetch
    const beanList = document.getElementById('beanList');
    if (
        (!data.origin || data.origin.length === 0) &&
        (!data.flavors || data.flavors.length === 0) &&
        !data.processing &&
        !data.roast_level &&
        (!data.elevation || data.elevation === '') && // Check if elevation is empty or not set
        data.arabica === 0 &&
        data.robusta === 0 &&
        data.singleorigin === 0 && // Include single origin check
        data.mix === 0 &&
        data.speciality === 0 &&
        data.decaf === 0 &&
        (!data.brew_methods || data.brew_methods.length === 0) && // Check brew methods
        data.roasters.length === 0
    ) {
        if (beanList) {
            beanList.innerHTML = ''; // Clear table
        }
        return; // Skip fetching
    }

    // Remove or update header for SCA Score as needed
    const tableHeadRow = document.querySelector('table thead tr');
    const existingSCAHeader = document.getElementById('scaScoreHeader');
    if (existingSCAHeader) {
        existingSCAHeader.remove();
    }
    if (data.speciality == 1) {
        const specialityHeader = document.getElementById('specialityHeader');
        if (specialityHeader) {
            const scaHeader = document.createElement('th');
            scaHeader.id = 'scaScoreHeader';
            scaHeader.classList.add('title');
            scaHeader.textContent = 'SCA Score';
            specialityHeader.parentNode.insertBefore(scaHeader, specialityHeader.nextSibling);
        }
    }
    const scaCol = document.getElementById('scaCol');
    if (scaCol) {
        scaCol.style.display = data.speciality == 1 ? 'table-column' : 'none';
    }

    data.sort_by = currentSort.field;
    data.sort_dir = currentSort.direction;

    // Store current speciality filter state globally
    window.currentSpecialityActive = (data.speciality == 1);

    try {
        // First try to get token from meta tag (Flask-WTF standard approach)
        let csrfToken = null;
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) {
            csrfToken = metaToken.getAttribute('content');
        }

        // If not found in meta tag, try cookie as fallback
        if (!csrfToken) {
            csrfToken = getCookie('csrftoken');
        }

        // If still not found, try X-CSRFToken cookie (Django style)
        if (!csrfToken) {
            csrfToken = getCookie('X-CSRFToken');
        }

        // If still not found, try _csrf_token cookie (another Flask convention)
        if (!csrfToken) {
            csrfToken = getCookie('_csrf_token');
        }

        if (!csrfToken) {
            throw new Error('CSRF token not found!'); // Throw error if token is missing
        }

        const response = await fetch('/filter_beans', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken // Use token from cookie or meta tag
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
        }

        const beans = await response.json();
        if (!beanList) return;
        if (beans.length === 0) {
            beanList.innerHTML = '<tr><td colspan="21">לא נמצאו פולים התואמים לסינון המבוקש.</td></tr>';
            return;
        }
        displayBeans(beans);

        // Ensure the first cell in each row has the sticky-col class
        beanList.querySelectorAll('tr').forEach(function(row) {
            let firstCell = row.querySelector('td:first-child');
            if (firstCell) {
                firstCell.classList.add('sticky-col');
            }
        });
    } catch (error) {
        console.error('Error fetching beans:', error);
        if (beanList) {
            beanList.innerHTML = `<tr><td colspan="21">שגיאה בטעינת הפולים: ${error.message}</td></tr>`;
        }
    }

    // Ensure the first cell in each row has the sticky class
    beanList.querySelectorAll('tr').forEach(function(row) {
        let firstCell = row.querySelector('td:first-child');
        if (firstCell) {
            firstCell.classList.add('sticky-col');
        }
    });
}

function displayBeans(beans) {
    const beanList = document.getElementById('beanList');
    beanList.innerHTML = '';

    // Sort client-side if needed
    if (currentSort.field === 'price100') {
        beans.sort((a, b) => {
            const aPrice = parseFloat(a.price && a.weight ? a.price / (a.weight / 100) : 0);
            const bPrice = parseFloat(b.price && b.weight ? b.price / (b.weight / 100) : 0);
            return currentSort.direction === 'asc' ? aPrice - bPrice : bPrice - aPrice;
        });
    }

    beans.forEach(bean => {
        const weightDisplay = bean.weight == 1000 ? "1 ק\"ג" : (bean.weight || '');
        const specialityDisplay = (bean.speciality === 1 || bean.speciality === true) ? '✓' : '';
        let scaCell = "";
        if (window.currentSpecialityActive) {
            scaCell = `<td>${bean.SCA_score || ''}</td>`;
        }
        // Calculate price per 100 grams if price and weight are available.
        let price100 = "";
        if (bean.price && bean.weight) {
            price100 = (bean.price / (bean.weight / 100)).toFixed(2);
        }
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${bean.bean_name || ''}</td>
            <td class="roaster-name" onclick="showRoasterDetails(${bean.roaster_id})">${bean.roaster_name || ''}</td>
            <td>${bean.origin || ''}</td>
            <td>${bean.processing || ''}<br>${bean.elevation || ''}</td>
            <td>${bean.flavors || ''}</td>
            <td>${bean.roast_level || ''}</td>
            <td>${bean.body || ''}</td>
            <td>${bean.acidity || ''}</td>
            <td>${bean.arabica || ''}</td>
            <td>${bean.robusta || ''}</td>
            <td>${bean.mix ? 'תערובת' : 'חד-זני'}</td>
            <td>${specialityDisplay}</td>
            ${scaCell}
            <td>${(bean.price || '')}<br>${weightDisplay}</td>
            <td>${price100}</td>
            <td>${bean.turkish ? '✓' : ''}</td>
            <td>${bean.espresso ? '✓' : ''}</td>
            <td>${bean.french_press ? '✓' : ''}</td>
            <td>${bean.pour_over ? '✓' : ''}</td>
            <td>${bean.drip ? '✓' : ''}</td>
            <td>${bean.cold_brew ? '✓' : ''}</td>
            <td></td>
        `;

        // Create and append the brew button safely
        const brewButtonCell = row.cells[row.cells.length - 1]; // Get the last cell
        const brewButton = document.createElement('button');
        brewButton.className = 'brew-button';
        brewButton.textContent = 'חליטה';
        // Use addEventListener to avoid escaping issues
        brewButton.addEventListener('click', function() {
            // Pass bean_name directly as a variable
            openBrewModal(bean.bean_id, bean.bean_name);
        });
        brewButtonCell.appendChild(brewButton);

        beanList.appendChild(row);
    });
}

function showRoasterDetails(roasterId) {
    console.log('Fetching details for roaster:', roasterId);
    const content = document.getElementById('roasterDetailContent');
    content.innerHTML = 'טוען...';
    document.getElementById('roasterDetailModal').style.display = 'block';

    fetch(`/get_roaster_details/${roasterId}`)
        .then(response => {
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(`Server error: ${response.status} - ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data && data.name) {
                content.innerHTML = `
                    <h2 style="font-size: 1.5em;">${data.website && data.website !== 'לא זמין' ? `<a href="${data.website}" target="_blank" style="text-decoration: none; color: inherit;">${data.name}</a>` : data.name}</h2>
                    <p><strong>כתובת:</strong> ${data.address || 'לא זמין'}</p>
                    <p><strong>עיר:</strong> ${data.city || 'לא זמין'}</p>
                    <p><strong>מיקוד:</strong> ${data.zip || 'לא זמין'}</p>
                    <p><strong>דוא"ל:</strong> ${data.email || 'לא זמין'}</p>
                    <p><strong>משלוח חינם מעל:</strong> ${data.minimum_shipping || 'לא זמין'}</p>
                    <p><strong>דמי משלוח:</strong> ${data.shipping_cost || 'לא זמין'}</p>
                `;
            } else {
                content.innerHTML = `<p style="color: red;">לא נמצאו פרטים עבור קולה זו.</p>`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = `<p style="color: red;">שגיאה בטעינת פרטי הקולה: ${error.message}</p>`;
        });
}

function updateSortArrows() {
    const price100SortArrow = document.getElementById('price100SortArrow');
    if (currentSort.field === 'price100') {
        price100SortArrow.textContent = (currentSort.direction === 'asc') ? '▲' : '▼';
    } else {
        price100SortArrow.textContent = '';
    }
    const arabicaSortArrow = document.getElementById('arabicaSortArrow');
    const robustaSortArrow = document.getElementById('robustaSortArrow');

    if (currentSort.field === 'arabica') {
        arabicaSortArrow.textContent = (currentSort.direction === 'asc') ? '▲' : '▼';
    } else {
        arabicaSortArrow.textContent = '';
    }

    if (currentSort.field === 'robusta') {
        robustaSortArrow.textContent = (currentSort.direction === 'asc') ? '▲' : '▼';
    } else {
        robustaSortArrow.textContent = '';
    }
}
